version: "3.8"

services:
  dev-api:
    build:
      context: .
    ports:
      - 8000:8000
    depends_on:
      db:
        condition: service_healthy
    networks:
      - rental-guru-backend-network

  db:
    image: postgres:13
    env_file: .env
    environment:
      - POSTGRES_DB=${DB_NAME}
      - POSTGRES_USER=${DB_USER}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    ports:
      - 5432:5432
    networks:
      - rental-guru-backend-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER} -d ${DB_NAME}"]
      interval: 5s
      timeout: 5s
      retries: 5

  adminer:
    image: adminer
    ports:
      - "8080:8080"
    environment:
      - ADMINER_DEFAULT_SERVER=db
      - ADMINER_DESIGN=pebena
    depends_on:
      - db
    networks:
      - rental-guru-backend-network

volumes:
  postgres_data:

networks:
  rental-guru-backend-network:
    driver: bridge
