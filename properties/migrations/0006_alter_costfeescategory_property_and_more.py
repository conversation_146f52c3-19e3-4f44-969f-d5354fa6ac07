# Generated by Django 4.2.20 on 2025-06-11 12:04

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("properties", "0005_add_amenities"),
    ]

    operations = [
        migrations.AlterField(
            model_name="costfeescategory",
            name="property",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="property_cost_fee_categories",
                to="properties.property",
            ),
        ),
        migrations.AlterField(
            model_name="propertydocument",
            name="document_type",
            field=models.CharField(
                choices=[
                    ("lease_agreement", "Lease Agreement"),
                    ("medical_clearance_form", "Medical Clearance Form"),
                    ("care_plan_agreement", "Care Plan Agreement"),
                    ("emergency_contact_form", "Emergency Contact Form"),
                    ("medication_schedule", "Medication Schedule"),
                    ("ada_compliance_certificate", "ADA Compliance Certificate"),
                    ("fall_risk_assessment", "Fall Risk Assessment"),
                    ("service_log", "Service Log"),
                    ("insurance_verification", "Insurance Verification"),
                    ("other", "Other"),
                ],
                max_length=100,
            ),
        ),
        migrations.AlterField(
            model_name="propertydocument",
            name="property",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="property_documents",
                to="properties.property",
            ),
        ),
        migrations.AlterField(
            model_name="propertyphoto",
            name="property",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="property_photos",
                to="properties.property",
            ),
        ),
        migrations.AlterField(
            model_name="rentdetails",
            name="property",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="property_rent_details",
                to="properties.property",
            ),
        ),
        migrations.CreateModel(
            name="Unit",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("number", models.CharField(max_length=20)),
                ("type", models.CharField(max_length=20)),
                (
                    "floor_number",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("size", models.CharField(blank=True, max_length=100, null=True)),
                ("bedrooms", models.PositiveIntegerField()),
                ("bathrooms", models.PositiveIntegerField(blank=True, null=True)),
                ("max_occupants", models.PositiveIntegerField(blank=True, null=True)),
                ("study_desks", models.PositiveIntegerField(blank=True, null=True)),
                ("page_saved", models.IntegerField(default=1)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "property",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="unit_property",
                        to="properties.property",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="costfeescategory",
            name="unit",
            field=models.ForeignKey(
                default=None,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="unit_cost_fee_categories",
                to="properties.unit",
            ),
        ),
        migrations.AddField(
            model_name="propertydocument",
            name="unit",
            field=models.ForeignKey(
                default=None,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="unit_documents",
                to="properties.unit",
            ),
        ),
        migrations.AddField(
            model_name="propertyphoto",
            name="unit",
            field=models.ForeignKey(
                default=None,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="unit_photos",
                to="properties.unit",
            ),
        ),
        migrations.AddField(
            model_name="rentdetails",
            name="unit",
            field=models.ForeignKey(
                default=None,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="unit_rent_details",
                to="properties.unit",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="rentdetails",
            unique_together={("property", "unit")},
        ),
    ]
