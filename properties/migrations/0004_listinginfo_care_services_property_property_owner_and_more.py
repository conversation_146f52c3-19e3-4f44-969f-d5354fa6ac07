# Generated by Django 4.2.20 on 2025-06-10 11:07

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("properties", "0003_calendarslot"),
    ]

    operations = [
        migrations.AddField(
            model_name="listinginfo",
            name="care_services",
            field=models.JSONField(blank=True, default=list, null=True),
        ),
        migrations.AddField(
            model_name="property",
            name="property_owner",
            field=models.ForeignKey(
                default=1,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="property_owner",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="property",
            name="published",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="property",
            name="published_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
    ]
