# Generated by Django 4.2.20 on 2025-07-15 11:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("properties", "0029_rentdetails_semester_alter_rentdetails_rental_type"),
    ]

    operations = [
        migrations.AlterField(
            model_name="costfee",
            name="payment_frequency",
            field=models.CharField(
                choices=[
                    ("one_time", "One Time"),
                    ("monthly", "Monthly"),
                    ("per_use", "Per Use"),
                    ("quarterly", "Quarterly"),
                    ("yearly", "Yearly"),
                ],
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="propertydocument",
            name="document_type",
            field=models.CharField(
                choices=[
                    ("lease_agreement", "Lease Agreement"),
                    ("medical_clearance_form", "Medical Clearance Form"),
                    ("care_plan_agreement", "Care Plan Agreement"),
                    ("emergency_contact_form", "Emergency Contact Form"),
                    ("medication_schedule", "Medication Schedule"),
                    ("ada_compliance_certificate", "ADA Compliance Certificate"),
                    ("fall_risk_assessment", "Fall Risk Assessment"),
                    ("service_log", "Service Log"),
                    ("insurance_verification", "Insurance Verification"),
                    ("other", "Other"),
                    ("floor_plan", "Floor Plan"),
                    ("compliance_certificate", "Compliance Certificate"),
                    ("maintenance_log", "Maintenance Log"),
                    ("move_in_move_out_checklist", "Move-In/Move-Out Checklist"),
                    ("utility_bill_copy", "Utility Bill Copy"),
                    ("inspection_report", "Inspection Report"),
                    ("renovation_permit", "Renovation Permit"),
                ],
                max_length=100,
            ),
        ),
    ]
