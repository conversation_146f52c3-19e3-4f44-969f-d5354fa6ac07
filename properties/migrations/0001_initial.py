# Generated by Django 4.2.20 on 2025-06-05 18:15

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Amenities",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("amenity", models.CharField(max_length=100)),
                ("sub_amenity", models.CharField(max_length=100)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name="Property",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                (
                    "property_type",
                    models.CharField(
                        choices=[
                            ("single_family_home", "Single-Family Home"),
                            ("apartment_unit", "Apartment Unit"),
                            ("multi_family", "Multi-Family"),
                            ("student_housing", "Student Housing"),
                            ("senior_living", "Senior Living"),
                        ],
                        max_length=50,
                    ),
                ),
                ("state", models.CharField(max_length=100)),
                ("city", models.CharField(max_length=100)),
                ("zip_code", models.CharField(blank=True, max_length=20, null=True)),
                ("street_address", models.CharField(max_length=255)),
                (
                    "other_amenities",
                    models.JSONField(
                        blank=True, help_text="List of other amenities", null=True
                    ),
                ),
                ("page_saved", models.IntegerField(default=1)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name="RentDetails",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "assigned_tenant",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "rental_type",
                    models.CharField(
                        choices=[
                            ("short_term", "Short-Term (Daily/Weekly)"),
                            ("long_term", "Long-Term (Monthly/Annual)"),
                        ],
                        max_length=50,
                    ),
                ),
                ("rent", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "security_deposit",
                    models.DecimalField(decimal_places=2, max_digits=10),
                ),
                ("available_from", models.DateField(blank=True, null=True)),
                ("available_to", models.DateField(blank=True, null=True)),
                ("lease_start_date", models.DateField()),
                ("lease_end_date", models.DateField()),
                ("promote_special_offer", models.BooleanField(default=False)),
                ("offer_start_date", models.DateField(blank=True, null=True)),
                ("offer_end_date", models.DateField(blank=True, null=True)),
                (
                    "offer_percentage",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "property",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="rent_details",
                        to="properties.property",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="PropertyTypeAndAmenity",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("property_type", models.CharField(max_length=50)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "sub_amenities",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sub_amenities",
                        to="properties.amenities",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="PropertyPhoto",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("photo", models.ImageField(upload_to="property_photos/")),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "property",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="photos",
                        to="properties.property",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="PropertyDocument",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("document", models.FileField(upload_to="property_documents/")),
                ("title", models.CharField(max_length=255)),
                ("document_type", models.CharField(max_length=100)),
                (
                    "visibility",
                    models.CharField(
                        choices=[
                            ("private", "Private"),
                            ("shared", "Shared with Tenant"),
                        ],
                        default="private",
                        max_length=20,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "property",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="documents",
                        to="properties.property",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="PropertyAssignedAmenities",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "property",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="property_amenities",
                        to="properties.property",
                    ),
                ),
                (
                    "sub_amenity",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="assigned_sub_amenity",
                        to="properties.amenities",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ListingInfo",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "listed_by",
                    models.CharField(
                        choices=[
                            (
                                "owner_manager_not_live",
                                "I'm the property owner/manager and don't live on the property",
                            ),
                            (
                                "owner_manager_live",
                                "I'm the property owner/manager and do live on the property",
                            ),
                            (
                                "agent_broker",
                                "I'm an agent/broker/management company and don't live on the property",
                            ),
                        ],
                        max_length=50,
                    ),
                ),
                ("total_bedrooms", models.PositiveIntegerField()),
                ("total_bathrooms", models.PositiveIntegerField()),
                (
                    "square_footage",
                    models.PositiveIntegerField(help_text="Size in sq.ft."),
                ),
                ("room_description", models.TextField(blank=True, null=True)),
                ("pets_allowed", models.BooleanField(default=False)),
                (
                    "pet_types",
                    models.JSONField(
                        blank=True, help_text="List of allowed pet types", null=True
                    ),
                ),
                (
                    "availability_duration",
                    models.IntegerField(
                        blank=True,
                        choices=[
                            (30, "30 mins"),
                            (60, "1 Hour"),
                            (90, "1.5 Hours"),
                            (120, "2 Hours"),
                        ],
                        help_text="Availability time duration.",
                        null=True,
                    ),
                ),
                (
                    "showing_availability",
                    models.JSONField(
                        blank=True,
                        help_text="Availability to show the property",
                        null=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "property",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="listing_info",
                        to="properties.property",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="CostFeesCategory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("category_name", models.CharField(max_length=100)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "property",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="cost_fee_categories",
                        to="properties.property",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="OwnerInfo",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("email", models.EmailField(max_length=254)),
                ("percentage", models.CharField(max_length=10)),
                (
                    "emergency_person",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "emergency_contact",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "owner",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="owner_detail",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "property",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="property_owned",
                        to="properties.property",
                    ),
                ),
            ],
            options={
                "unique_together": {("property", "owner")},
            },
        ),
        migrations.CreateModel(
            name="CostFee",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("fee_name", models.CharField(max_length=100)),
                (
                    "payment_frequency",
                    models.CharField(
                        choices=[
                            ("one_time", "One Time"),
                            ("monthly", "Monthly"),
                            ("per_use", "Per Use"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "fee_amount",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                (
                    "fee_type",
                    models.CharField(
                        choices=[
                            ("flat_fee", "Flat Fee"),
                            ("flat_fee_per_item", "Flat Fee Per Item"),
                            ("fee_range", "Fee Range"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "is_required",
                    models.CharField(
                        choices=[
                            ("in_rent", "Included in base rent"),
                            ("required", "Required"),
                            ("optional", "Optional"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "refundable_status",
                    models.CharField(
                        choices=[
                            ("non_refundable", "Non-refundable"),
                            ("partially_refundable", "Partially refundable"),
                            ("refundable", "Refundable"),
                        ],
                        max_length=50,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="fees",
                        to="properties.costfeescategory",
                    ),
                ),
            ],
            options={
                "unique_together": {("category", "fee_name")},
            },
        ),
    ]
