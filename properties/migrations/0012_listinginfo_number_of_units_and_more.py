# Generated by Django 4.2.20 on 2025-06-12 09:42

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("properties", "0011_alter_property_zip_code"),
    ]

    operations = [
        migrations.Add<PERSON>ield(
            model_name="listinginfo",
            name="number_of_units",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="listinginfo",
            name="availability_duration",
            field=models.IntegerField(
                choices=[
                    (30, "30 mins"),
                    (60, "1 Hour"),
                    (90, "1.5 Hours"),
                    (120, "2 Hours"),
                ],
                default=30,
                help_text="Availability time duration.",
            ),
        ),
        migrations.AlterField(
            model_name="listinginfo",
            name="room_description",
            field=models.TextField(default=None),
        ),
        migrations.AlterField(
            model_name="listinginfo",
            name="showing_availability",
            field=models.JSONField(
                default=None, help_text="Availability to show the property"
            ),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="listinginfo",
            name="square_footage",
            field=models.PositiveIntegerField(
                blank=True, help_text="Size in sq.ft.", null=True
            ),
        ),
    ]
