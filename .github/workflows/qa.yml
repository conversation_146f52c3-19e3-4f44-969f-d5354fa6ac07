name: Docker Build and Run QA

on:
    push:
        branches:
            - qa

jobs:
    docker-job:
        runs-on: api-dev

        steps:
            - uses: actions/checkout@v3

            - name: Create env file
              run: |
                  cat > .env << EOF
                  ${{ vars.ENV_QA }}
                  EOF

            - name: Stop and remove existing containers (only if running)
              run: |
                  if [ "$(docker ps -q)" ]; then
                    echo "Containers are running. Stopping and removing them..."
                    docker-compose -f docker-compose-qa.yaml down || true
                    docker system prune -f
                  else
                    echo "No running containers. Skipping stop and prune."
                  fi

            - name: Start services with Docker Compose
              run: docker-compose -f docker-compose-qa.yaml up -d --build

            - name: Show container logs
              run: |
                  sleep 30
                  docker-compose -f docker-compose-qa.yaml logs qa-api

            - name: Clean up unused resources
              run: docker system prune -f
