# Generated by Django 4.2.20 on 2025-05-28 06:28

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("user_authentication", "0010_add_servicecategory_servicesubcategory"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="vendor",
            name="service_categories",
        ),
        migrations.RemoveField(
            model_name="vendor",
            name="services_offered",
        ),
        migrations.CreateModel(
            name="VendorServices",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("modified_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "category_id",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="service_category",
                        to="user_authentication.servicecategory",
                    ),
                ),
                (
                    "subcategory_id",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="service_subcategory",
                        to="user_authentication.servicesubcategory",
                    ),
                ),
                (
                    "user_id",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="service_vendor",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
