# Generated by Django 4.2.20 on 2025-05-13 15:18

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("user_authentication", "0002_alter_customuser_username"),
    ]

    operations = [
        migrations.CreateModel(
            name="PropertyOwner",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "profile_image_path",
                    models.ImageField(
                        blank=True, null=True, upload_to="profile_images/"
                    ),
                ),
                (
                    "registration_type",
                    models.CharField(
                        choices=[
                            ("individual", "Individual"),
                            ("business", "Business"),
                        ],
                        default="individual",
                        max_length=20,
                    ),
                ),
                (
                    "business_name",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "business_website",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "business_address",
                    models.Char<PERSON>ield(blank=True, max_length=100, null=True),
                ),
                (
                    "company_registration_number",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "business_type",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "business_license",
                    models.FileField(
                        blank=True, null=True, upload_to="documents/licenses/owner/"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("modified_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "user_id",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="property_owner_user",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.RenameField(
            model_name="kycrequest",
            old_name="back_img_path",
            new_name="back_image",
        ),
        migrations.RenameField(
            model_name="kycrequest",
            old_name="front_img_path",
            new_name="front_image",
        ),
        migrations.RemoveField(
            model_name="kycrequest",
            name="approved",
        ),
        migrations.AddField(
            model_name="kycrequest",
            name="notes",
            field=models.TextField(blank=True, default=None, null=True),
        ),
        migrations.AddField(
            model_name="kycrequest",
            name="status",
            field=models.CharField(
                choices=[
                    ("pending", "Pending"),
                    ("approved", "Approved"),
                    ("rejected", "Rejected"),
                ],
                default="pending",
                max_length=20,
            ),
        ),
        migrations.AlterField(
            model_name="kycrequest",
            name="id_type",
            field=models.CharField(
                choices=[
                    ("cnic", "CNIC"),
                    ("passport", "Passport"),
                    ("driving_license", "Driving License"),
                ],
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="kycrequest",
            name="review_notes",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="kycrequest",
            name="reviewed_at",
            field=models.DateField(blank=True, null=True),
        ),
        migrations.DeleteModel(
            name="Profile",
        ),
    ]
