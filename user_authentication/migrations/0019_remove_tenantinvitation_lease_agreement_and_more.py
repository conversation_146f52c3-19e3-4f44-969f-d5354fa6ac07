# Generated by Django 4.2.20 on 2025-07-31 13:02

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("user_authentication", "0018_alter_tenantinvitation_unique_together_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="tenantinvitation",
            name="lease_agreement",
        ),
        migrations.RemoveField(
            model_name="tenantinvitation",
            name="signed_agreement",
        ),
        migrations.AlterField(
            model_name="tenant",
            name="investment_preferences",
            field=models.JSONField(blank=True, default=list, null=True),
        ),
        migrations.CreateModel(
            name="Agreements",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("lease_agreement", models.FileField(upload_to="tenant_agreements/")),
                (
                    "signed_agreement",
                    models.FileField(
                        blank=True, null=True, upload_to="tenant_signed_agreements/"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "invitation",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="agreements",
                        to="user_authentication.tenantinvitation",
                    ),
                ),
            ],
        ),
    ]
