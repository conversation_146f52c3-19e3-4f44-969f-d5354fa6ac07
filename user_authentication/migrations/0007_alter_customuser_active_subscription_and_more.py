# Generated by Django 4.2.20 on 2025-05-22 11:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("user_authentication", "0006_alter_role_unique_together"),
    ]

    operations = [
        migrations.AlterField(
            model_name="customuser",
            name="active_subscription",
            field=models.CharField(
                blank=True,
                choices=[
                    ("basic", "Basic"),
                    ("enterprise", "Enterprise"),
                    ("pro", "Pro"),
                ],
                max_length=20,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="customuser",
            name="auth_method",
            field=models.CharField(
                choices=[
                    ("facebook", "Facebook"),
                    ("linkedin", "Linkedin"),
                    ("google", "Google"),
                    ("apple", "Apple"),
                    ("local", "Local"),
                ],
                default="local",
                max_length=20,
            ),
        ),
        migrations.AlterField(
            model_name="kycrequest",
            name="id_type",
            field=models.CharField(
                choices=[
                    ("cnic", "Cnic"),
                    ("passport", "Passport"),
                    ("driving_license", "Driving License"),
                ],
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="role",
            name="role",
            field=models.Char<PERSON>ield(
                choices=[
                    ("property_owner", "Owner"),
                    ("vendor", "Vendor"),
                    ("tenant", "Tenant"),
                ],
                max_length=20,
            ),
        ),
    ]
