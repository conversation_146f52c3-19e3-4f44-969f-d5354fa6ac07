# Generated by Django 4.2.20 on 2025-07-23 15:09

import datetime
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("properties", "0031_unit_beds_alter_unit_bedrooms"),
        ("user_authentication", "0016_vendorinvitation_expired_at"),
    ]

    operations = [
        migrations.AddField(
            model_name="propertyowner",
            name="page_saved",
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name="vendor",
            name="page_saved",
            field=models.IntegerField(default=0),
        ),
        migrations.AlterField(
            model_name="vendorinvitation",
            name="expired_at",
            field=models.DateTimeField(
                blank=True,
                default=datetime.datetime(
                    2025, 7, 28, 15, 9, 4, 413911, tzinfo=datetime.timezone.utc
                ),
                null=True,
            ),
        ),
        migrations.CreateModel(
            name="Tenant",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("job_title", models.CharField(blank=True, max_length=50, null=True)),
                (
                    "employment_status",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("full-time", "Full-Time"),
                            ("part-time", "Part-Time"),
                            ("self-employed", "Self-Employed"),
                            ("unemployed", "Unemployed"),
                            ("student", "Student"),
                            ("retired", "Retired"),
                        ],
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "industry",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("technology", "Technology"),
                            ("finance", "Finance"),
                            ("healthcare", "Healthcare"),
                            ("education", "Education"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "income_range",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("<$50,000", "<$50,000"),
                            ("$50,000-$70,000", "$50,000-$70,000"),
                            ("$70,000-$90,000", "$70,000-$90,000"),
                            (">$90,000", ">$90,000"),
                        ],
                        max_length=30,
                        null=True,
                    ),
                ),
                (
                    "mortgage_amount",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "credit_score_range",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("500-600", "500-600"),
                            ("600-700", "600-700"),
                            ("700-750", "700-750"),
                            ("750+", "750+"),
                        ],
                        max_length=15,
                        null=True,
                    ),
                ),
                (
                    "debt_to_income_ratio",
                    models.CharField(blank=True, max_length=25, null=True),
                ),
                (
                    "investment_preferences",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("stocks", "Stocks"),
                            ("real_estate", "Real Estate"),
                            ("cryptocurrencies", "Cryptocurrencies"),
                            ("bonds", "Bonds"),
                        ],
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "property_type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("renting", "Renting"),
                            ("owning", "Owning"),
                            (
                                "living_with_family/friends",
                                "Living with Family/Friends",
                            ),
                        ],
                        max_length=30,
                        null=True,
                    ),
                ),
                (
                    "length_of_stay",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("less_than_1_year", "Less than 1 year"),
                            ("1-3 years", "1-3 years"),
                            ("3-5 years", "3-5 years"),
                            ("5+ years", "5+ years"),
                        ],
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "utility_cost_estimates",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("<$100", "<$100"),
                            ("$100-$200", "$100-$200"),
                            ("$200-$500", "$200-$500"),
                            (">$300", ">$300"),
                        ],
                        max_length=20,
                        null=True,
                    ),
                ),
                ("lease_term", models.IntegerField(blank=True, null=True)),
                (
                    "preferred_rental_price_range",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("$1000-$1500", "$1000-$1500"),
                            ("$1500-$2000", "$1500-$2000"),
                            ("$2000-$2500", "$2000-$2500"),
                            ("$2500+", "$2500+"),
                        ],
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "current_home_value",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "interest_in_moving",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("within_6_months", "Yes, in the next 6 months"),
                            ("not_planning_to_move", "No, not planning to move"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "late_bill_payment_history",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("never", "Never Missed a Payment"),
                            ("occasionally", "Occasionally Late (1-2 times a year)"),
                            ("frequently", "Frequently Late (3+ times a year)"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "spending_habits",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("strict", "I stick to a strict budget"),
                            (
                                "moderate",
                                "I track spending but make occasional impulse buys",
                            ),
                            ("flexible", "I spend freely and adjust as needed"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "monthly_budget_allocations",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "financial_goals",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("pay_off_debt", "Pay Off Debt"),
                            ("build_an_emergency_fund", "Build an Emergency Fund"),
                            ("increase_investments", "Increase Investments"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                ("ai_for_suggestions", models.BooleanField(default=False)),
                ("page_saved", models.IntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("modified_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "user_id",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="tenant_user",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="TenantInvitation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("first_name", models.CharField(max_length=100)),
                ("last_name", models.CharField(max_length=100)),
                ("email", models.EmailField(max_length=254)),
                (
                    "tenant_type",
                    models.CharField(
                        choices=[
                            ("individual", "Individual"),
                            ("family", "Business"),
                            ("shared_housing", "Shared Housing"),
                            ("small_business", "Small Business"),
                            ("corporate_office", "Corporate Office"),
                            ("retail_store", "Retail Store"),
                            ("restaurant", "Restaurant"),
                            ("warehouse", "Warehouse"),
                        ],
                        max_length=30,
                    ),
                ),
                ("lease_amount", models.IntegerField()),
                ("security_deposit", models.IntegerField(blank=True, null=True)),
                ("lease_start_date", models.DateField()),
                ("lease_end_date", models.DateField()),
                ("lease_agreement", models.FileField(upload_to="tenant_agreements/")),
                ("accepted", models.BooleanField(default=False)),
                ("blocked", models.BooleanField(default=False)),
                ("agreed", models.BooleanField(default=False)),
                (
                    "signed_agreement",
                    models.FileField(
                        blank=True, null=True, upload_to="tenant_signed_agreements/"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "expired_at",
                    models.DateTimeField(
                        blank=True,
                        default=datetime.datetime(
                            2025, 7, 28, 15, 9, 4, 414806, tzinfo=datetime.timezone.utc
                        ),
                        null=True,
                    ),
                ),
                (
                    "assigned_property",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="tenant_assigned_property",
                        to="properties.property",
                    ),
                ),
                (
                    "sender",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="tenant_invitations_sent",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "unique_together": {
                    ("email", "tenant_type", "sender", "assigned_property")
                },
            },
        ),
    ]
