# Generated by Django 4.2.20 on 2025-05-13 10:06

from django.conf import settings
import django.contrib.auth.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import user_authentication.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="CustomUser",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("password", models.CharField(max_length=128, verbose_name="password")),
                (
                    "last_login",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last login"
                    ),
                ),
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                (
                    "username",
                    models.Char<PERSON>ield(
                        error_messages={
                            "unique": "A user with that username already exists."
                        },
                        help_text="Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.",
                        max_length=150,
                        unique=True,
                        validators=[
                            django.contrib.auth.validators.UnicodeUsernameValidator()
                        ],
                        verbose_name="username",
                    ),
                ),
                (
                    "first_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="first name"
                    ),
                ),
                (
                    "last_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="last name"
                    ),
                ),
                (
                    "is_staff",
                    models.BooleanField(
                        default=False,
                        help_text="Designates whether the user can log into this admin site.",
                        verbose_name="staff status",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Designates whether this user should be treated as active. Unselect this instead of deleting accounts.",
                        verbose_name="active",
                    ),
                ),
                (
                    "date_joined",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="date joined"
                    ),
                ),
                ("email", models.EmailField(max_length=254, unique=True)),
                (
                    "phone_number",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("email_verified", models.BooleanField(default=False)),
                ("otp_enable", models.BooleanField(default=False)),
                ("otp", models.CharField(blank=True, max_length=4, null=True)),
                ("otp_expiry", models.DateTimeField(blank=True, null=True)),
                (
                    "business_name",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "auth_method",
                    models.CharField(
                        choices=[
                            ("facebook", "Facebook"),
                            ("linkedin", "LinkedIn"),
                            ("google", "Google"),
                            ("apple", "Apple"),
                            ("local", "Local"),
                        ],
                        default="local",
                        max_length=20,
                    ),
                ),
                (
                    "active_subscription",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("basic", "Basic Plan"),
                            ("enterprise", "Enterprise Plan"),
                            ("pro", "Pro Plan"),
                        ],
                        max_length=20,
                        null=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("modified_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.group",
                        verbose_name="groups",
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Specific permissions for this user.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.permission",
                        verbose_name="user permissions",
                    ),
                ),
            ],
            options={
                "db_table": "user",
            },
            managers=[
                ("objects", user_authentication.models.CustomUserManager()),
            ],
        ),
        migrations.CreateModel(
            name="Role",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "role",
                    models.CharField(
                        choices=[
                            ("property owner", "Property Owner"),
                            ("vendor", "Vendor"),
                            ("tenant", "Tenant"),
                        ],
                        max_length=20,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("modified_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "user_id",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="role_user",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Profile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("vendor_role", models.CharField(blank=True, max_length=50, null=True)),
                (
                    "profile_image_path",
                    models.ImageField(
                        blank=True, null=True, upload_to="profile_images/"
                    ),
                ),
                (
                    "registration_type",
                    models.CharField(
                        choices=[
                            ("individual", "Individual"),
                            ("business", "Business"),
                        ],
                        default="individual",
                        max_length=20,
                    ),
                ),
                (
                    "business_name",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "business_website",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "business_address",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "company_registration_number",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "business_type",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "business_license",
                    models.FileField(
                        blank=True, null=True, upload_to="business_licenses/"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("modified_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "user_id",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="vendor_user",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="KYCRequest",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("id_type", models.CharField(max_length=255)),
                ("front_img_path", models.FileField(upload_to="kyc_docs/")),
                (
                    "back_img_path",
                    models.FileField(blank=True, null=True, upload_to="kyc_docs/"),
                ),
                ("approved", models.BooleanField(default=False)),
                ("reviewed_at", models.DateField()),
                ("review_notes", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("modified_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "user_id",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="kyc_user",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
