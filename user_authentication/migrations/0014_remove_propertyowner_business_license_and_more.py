# Generated by Django 4.2.20 on 2025-07-16 14:38

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("user_authentication", "0013_vendorinvitation"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="propertyowner",
            name="business_license",
        ),
        migrations.RemoveField(
            model_name="vendor",
            name="business_license",
        ),
        migrations.RemoveField(
            model_name="vendor",
            name="insurance_certificates",
        ),
        migrations.RemoveField(
            model_name="vendor",
            name="other_certificates",
        ),
        migrations.CreateModel(
            name="LicenseAndCertificates",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "profile_type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("property_owner", "Property Owner"),
                            ("vendor", "Vendor"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "document",
                    models.FileField(upload_to="documents/licenses_and_certificates/"),
                ),
                ("document_type", models.Char<PERSON>ield(max_length=100)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("modified_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "user_id",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="license_and_certificates_user",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
