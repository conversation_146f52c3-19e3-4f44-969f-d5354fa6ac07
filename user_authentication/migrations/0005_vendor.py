# Generated by Django 4.2.20 on 2025-05-16 08:28

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("user_authentication", "0004_alter_propertyowner_user_id"),
    ]

    operations = [
        migrations.CreateModel(
            name="Vendor",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("vendor_role", models.CharField(blank=True, max_length=50, null=True)),
                (
                    "profile_image_path",
                    models.ImageField(
                        blank=True, null=True, upload_to="profile_images/"
                    ),
                ),
                (
                    "registration_type",
                    models.CharField(
                        choices=[
                            ("individual", "Individual"),
                            ("business", "Business"),
                        ],
                        default="individual",
                        max_length=20,
                    ),
                ),
                (
                    "business_name",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "business_website",
                    models.Char<PERSON>ield(blank=True, max_length=100, null=True),
                ),
                (
                    "business_address",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "company_registration_number",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "business_type",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "business_license",
                    models.FileField(
                        blank=True, null=True, upload_to="documents/licenses/vendor/"
                    ),
                ),
                ("service_categories", models.JSONField()),
                ("services_offered", models.JSONField()),
                (
                    "service_area",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "years_of_experience",
                    models.CharField(blank=True, max_length=10, null=True),
                ),
                ("availability", models.BooleanField(default=False)),
                ("daily_availability", models.JSONField(blank=True, null=True)),
                ("emergency_services", models.BooleanField(default=False)),
                (
                    "emergency_contact",
                    models.CharField(blank=True, max_length=10, null=True),
                ),
                ("languages", models.CharField(blank=True, max_length=100, null=True)),
                ("insurance_coverage", models.BooleanField(default=False)),
                (
                    "insurance_certificates",
                    models.FileField(
                        blank=True,
                        null=True,
                        upload_to="documents/certificates/insurance/",
                    ),
                ),
                (
                    "other_certificates",
                    models.FileField(
                        blank=True, null=True, upload_to="documents/certificates/other/"
                    ),
                ),
                (
                    "description",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("modified_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "user_id",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="vendor_user",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
