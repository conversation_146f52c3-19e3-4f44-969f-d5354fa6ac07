# Generated by Django 4.2.20 on 2025-07-24 11:35

from django.db import migrations, models
import user_authentication.models


class Migration(migrations.Migration):

    dependencies = [
        (
            "user_authentication",
            "0017_propertyowner_page_saved_vendor_page_saved_and_more",
        ),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name="tenantinvitation",
            unique_together=set(),
        ),
        migrations.AddField(
            model_name="tenantinvitation",
            name="assignment_id",
            field=models.PositiveIntegerField(default=None),
        ),
        migrations.AddField(
            model_name="tenantinvitation",
            name="assignment_type",
            field=models.CharField(
                choices=[("unit", "Unit"), ("property", "Property")],
                default="unit",
                max_length=20,
            ),
        ),
        migrations.AlterField(
            model_name="tenantinvitation",
            name="expired_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="vendorinvitation",
            name="expired_at",
            field=models.DateTimeField(
                blank=True,
                default=user_authentication.models.get_default_expiry_date,
                null=True,
            ),
        ),
        migrations.AlterUniqueTogether(
            name="tenantinvitation",
            unique_together={
                ("email", "tenant_type", "sender", "assignment_type", "assignment_id")
            },
        ),
        migrations.RemoveField(
            model_name="tenantinvitation",
            name="assigned_property",
        ),
    ]
