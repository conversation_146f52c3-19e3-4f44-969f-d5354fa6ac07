# Generated by Django 4.2.20 on 2025-07-15 13:08

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("user_authentication", "0012_alter_vendor_years_of_experience"),
    ]

    operations = [
        migrations.CreateModel(
            name="VendorInvitation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("first_name", models.Char<PERSON><PERSON>(max_length=100)),
                ("last_name", models.Char<PERSON><PERSON>(max_length=100)),
                ("email", models.EmailField(max_length=254)),
                (
                    "role",
                    models.Char<PERSON>ield(
                        choices=[
                            ("personal_training", "Personal Training"),
                            ("home_cleaning", "Home Cleaning"),
                            ("personal_chef", "Personal Chef"),
                            ("yoga_instruction", "Yoga Instruction"),
                            ("electrical_services", "Electrical Services"),
                            ("hvac_technician", "HVAC Technician"),
                            ("landscaping", "Landscaping"),
                            ("pest_control", "Pest Control"),
                            ("appliance_repair", "Appliance Repair"),
                            ("security_services", "Security Services"),
                            ("painting_renovation", "Painting & Renovation"),
                            ("general_handyman", "General Handyman"),
                            ("moving_services", "Moving Services"),
                            ("it_network_setup", "IT & Network Setup"),
                            ("furniture_assembly", "Furniture Assembly"),
                            ("window_cleaning", "Window Cleaning"),
                            ("pool_maintenance", "Pool Maintenance"),
                            ("carpet_cleaning", "Carpet Cleaning"),
                            ("elderly_care_services", "Elderly Care Services"),
                        ],
                        max_length=100,
                    ),
                ),
                ("accepted", models.BooleanField(default=False)),
                ("blocked", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "sender",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="vendor_invitations_sent",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "unique_together": {("email", "role")},
            },
        ),
    ]
